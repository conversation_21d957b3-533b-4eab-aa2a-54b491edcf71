"use server";

import { cookies } from "next/headers";

const BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "";

export const getUserProfile = async <T>() => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;

  try {
    const res = await fetch(`${BASE_URL}/profile`, {
      headers: {
        Authorization: `JWT ${token}`,
      },
      next: { revalidate: 10, tags: ["user-profile"] },
    });

    return res.json() as T;
  } catch (error) {
    console.log("error", error);
    throw new Error("Failed to fetch user profile");
  }
};

export const searchContacts = async <T>(query: string) => {
  const cookieStore = cookies();
  const token = cookieStore.get("token")?.value;

  try {
    const res = await fetch(
      `${BASE_URL}/contacts?type=tenant&type=buyer&type=seller&limit=10&offset=0&search_name_or_email=${query}`,
      {
        headers: {
          Authorization: `JWT ${token}`,
        },
        next: { revalidate: 10, tags: [`search-contacts-${query}`] },
      }
    );

    if (!res.ok) {
      console.log("res", res);
      throw new Error("Failed to fetch contacts");
    }
    return res.json() as T;
  } catch (error) {
    console.log("error", error);
    throw new Error("Failed to fetch contacts");
  }
};
