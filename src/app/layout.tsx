import "./globals.css";

import type { Metadata, Viewport } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "next/font/google";

import "./globals.css";
import Header from "./header";
import GlobalProviders from "@/clients/GlobalProviders";

const poppins = Poppins({
  weight: ["200", "300", "400", "500", "600", "700"],
  subsets: ["latin"],
});

const roboto = Roboto({
  weight: ["100", "300", "400", "500", "700", "900"],
  subsets: ["latin"],
  variable: "--font-roboto",
});

export const metadata: Metadata = {
  title: "C&C Agent Cloud",
  description: "Your central place to manage your Nantucket Listing",
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${poppins.className} antialiased bg-white`}>
        <GlobalProviders>
          <Header />
          <div id="pageContent" className="pt-[64px] md:pt-[72px]">
            {children}
          </div>
        </GlobalProviders>
      </body>
    </html>
  );
}
