"use client";

import { MouseEvent, memo, useMemo } from "react";

import {
  BlockedType,
  CalendarLeasedType,
  CalendarViewType,
  PropertyRentalRates,
} from "@/types/calendar";
import { Nullable } from "@/types/common";
import {
  getAvailabilityDataForCurrentMonth,
  getFirstDayOfMonth,
  getNumberOfDaysInMonth,
  getRentalRatesForCurrentMonth,
} from "@/utils/calendar";

import classNames from "classnames";
import dayjs, { Dayjs } from "dayjs";
import localeData from "dayjs/plugin/localeData";

import DateItem from "./DateItem";
import { PopupProps } from "@/clients/views/calendar/CalendarWrapper";
import StatusPill from "@/app/components/calendar/StatusPill";
import { PropertyAvailability } from "@/types/calendar";

dayjs.extend(localeData);
const MONTHS = dayjs.months();
const DAY_NAMES = dayjs.localeData().weekdaysShort();

type Props = {
  month: number;
  year: number;
  selected: [Nullable<Dayjs>, Nullable<Dayjs>];
  mouseOverDate: Nullable<Dayjs>;
  setMouseOverDate: any;
  cellHeightClassName?: string;
  view: CalendarViewType;
  hideHeader?: boolean;
  onSelectDate: (e: MouseEvent<HTMLElement>, dateObject: Dayjs) => void;
  availabilities?: PropertyAvailability[];
  rentalRates?: PropertyRentalRates[];
  setShowPopup?: (s: Nullable<PopupProps>) => void;
};

const Calendar = ({
  month,
  year,
  selected,
  mouseOverDate,
  setMouseOverDate,
  cellHeightClassName,
  view,
  hideHeader,
  onSelectDate,
  rentalRates = [],
  setShowPopup,
  availabilities = [],
}: Props) => {
  const availabilityDataForCurrentMonth = useMemo(
    () => getAvailabilityDataForCurrentMonth(availabilities, month + 1, year),
    [availabilities, month, year]
  );
  const rentalRatesForCurrentMonth = useMemo(
    () => getRentalRatesForCurrentMonth(rentalRates, month + 1, year),
    [month, rentalRates, year]
  );

  const numberOfDays = useMemo(
    () => getNumberOfDaysInMonth(year, month),
    [month, year]
  );
  const firstDayOfMonth = useMemo(
    () => getFirstDayOfMonth(year, month),
    [year, month]
  );
  const numberOfDaysLastMonth = useMemo(
    () =>
      month > 0
        ? getNumberOfDaysInMonth(year, month - 1)
        : getNumberOfDaysInMonth(year - 1, 11),
    [year, month]
  );

  const totalSlots: JSX.Element[] = [];

  for (
    let i = numberOfDaysLastMonth - firstDayOfMonth;
    i < numberOfDaysLastMonth;
    i++
  ) {
    totalSlots.push(
      <DateItem
        key={`prev${i}`}
        dateObject={dayjs(
          month > 0
            ? `${year}/${month}/${i + 1}`
            : `${year - 1}/${12}/${i + 1}`,
          "YYYY/M/D"
        )}
        date={i + 1}
        insideCurrentMonth={false}
        selected={selected}
        mouseOverDate={mouseOverDate}
        setMouseOverDate={setMouseOverDate}
        setShowPopup={setShowPopup}
        cellHeightClassName={cellHeightClassName}
        view={view}
        onSelectDate={onSelectDate}
        availabilities={availabilityDataForCurrentMonth}
        rentalRate={rentalRatesForCurrentMonth.find((_r) =>
          dayjs(_r.from_date).isSame(
            dayjs(
              month > 0
                ? `${year}/${month}/${i + 1}`
                : `${year - 1}/${12}/${i + 1}`,
              "YYYY/M/D"
            )
          )
        )}
      />
    );
  }

  for (let i = 0; i < numberOfDays; i++) {
    totalSlots.push(
      <DateItem
        key={`day${i}`}
        dateObject={dayjs(`${year}/${month + 1}/${i + 1}`, "YYYY/M/D")}
        date={i + 1}
        active
        insideCurrentMonth={true}
        selected={selected}
        mouseOverDate={mouseOverDate}
        setMouseOverDate={setMouseOverDate}
        setShowPopup={setShowPopup}
        cellHeightClassName={cellHeightClassName}
        view={view}
        onSelectDate={onSelectDate}
        availabilities={availabilityDataForCurrentMonth}
        rentalRate={rentalRatesForCurrentMonth.find((_r) =>
          dayjs(_r.from_date).isSame(
            dayjs(`${year}/${month + 1}/${i + 1}`, "YYYY/M/D").format(
              "YYYY-MM-DD"
            )
          )
        )}
      />
    );
  }

  for (let i = 0; i < (42 - firstDayOfMonth - numberOfDays) % 7; i++) {
    totalSlots.push(
      <DateItem
        key={`next${i}`}
        dateObject={dayjs(`${year}/${month + 1}/${i + 1}`, "YYYY/M/D").add(
          1,
          "month"
        )}
        date={i + 1}
        insideCurrentMonth={false}
        selected={selected}
        mouseOverDate={mouseOverDate}
        setMouseOverDate={setMouseOverDate}
        setShowPopup={setShowPopup}
        cellHeightClassName={cellHeightClassName}
        view={view}
        onSelectDate={onSelectDate}
        availabilities={availabilityDataForCurrentMonth}
        rentalRate={rentalRatesForCurrentMonth.find((_r) =>
          dayjs(_r.from_date).isSame(
            dayjs(`${year}/${month + 1}/${i + 1}`, "YYYY/M/D").add(1, "month")
          )
        )}
      />
    );
  }

  const rows: JSX.Element[][] = [];

  const weeks = Math.ceil(totalSlots.length / 7);
  for (let i = 0; i < weeks; i++) {
    const cells: JSX.Element[] = [];
    for (let j = 0; j < 7; j++) {
      const day = i * 7 + j;
      const row = totalSlots[day];
      cells.push(row || <td key={`blank${day}`}>{""}</td>);
    }
    let day = i * 7 - firstDayOfMonth;
    if (day < 0) {
      day = 0;
    }
    const date = dayjs(`${year}/${month + 1}/${day + 1}`, "YYYY/M/D");
    rows.push(cells);
  }

  return (
    <div className="Calendar">
      <div
        className={classNames("flex-center-between p-4", {
          "md:hidden": hideHeader && view !== CalendarViewType.YEAR,
        })}
      >
        <p
          className={classNames(`tracking-[1.68px] font-bold`, {
            "text-base md:text-[28px]": view === CalendarViewType.MONTH,
            "text-base md:text-[23px]": view === CalendarViewType.YEAR,
          })}
        >
          {MONTHS[month]} {year}
        </p>
      </div>

      <table className="min-w-[250px] w-full md:border-separate md:border-spacing-2">
        <thead className="pb-2.5">
          <tr className="text-center">
            {DAY_NAMES.map((day) => (
              <th
                key={day}
                className="text-xs text-[#666666] font-normal w-[12.5%]"
              >
                <span>{day}</span>
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {rows.map((d, i) => (
            <tr key={i} className="text-center">
              {d}
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default memo(Calendar);
