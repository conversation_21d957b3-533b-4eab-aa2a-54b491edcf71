import { memo, useRef } from "react";

import { AutocompleteOption } from ".";
import { twMerge } from "tailwind-merge";
import classNames from "classnames";

type Props = {
  options?: AutocompleteOption[];
  activeIndex: number;
  setActiveIndex: (index: number) => void;
  onSelect?: (_o: AutocompleteOption) => void;
  onClosePopup: () => void;
  isFetchingData?: boolean;
  itemClassName?: string;
  dropdownClassName?: string;
};

const AutocompleteItems = ({
  options = [],
  activeIndex,
  setActiveIndex,
  onSelect,
  onClosePopup,
  isFetchingData,
  itemClassName,
  dropdownClassName,
}: Props) => {
  const selectRef = useRef<null | HTMLUListElement>(null);
  const activeListItemRef = useRef<null | HTMLLIElement>(null);

  return (
    <ul
      className={twMerge(
        "absolute z-[99999] top-[50px] left-0 right-0 border rounded bg-white max-h-[200px] overflow-y-auto",
        dropdownClassName
      )}
      tabIndex={0}
      ref={selectRef}
    >
      {options.map((_option, index) => (
        <li
          ref={index === activeIndex ? activeListItemRef : null}
          className={twMerge(
            classNames(
              `px-3 py-1 cursor-pointer hover:bg-carolina-blue hover:text-white`,
              {
                "bg-slate-100": index === activeIndex,
              }
            ),
            itemClassName
          )}
          onClick={() => {
            onSelect?.(_option);
            onClosePopup();
          }}
          key={index}
        >
          {_option.label}
        </li>
      ))}
    </ul>
  );
};

export default memo(AutocompleteItems);
