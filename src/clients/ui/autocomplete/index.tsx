"use client";

import debounce from "lodash/debounce";
import {
  ComponentProps,
  useCallback,
  useEffect,
  useMemo,
  useState,
} from "react";

import Input from "../input";

import AutocompleteItems from "./AutocompleteItems";
import { Nullable } from "@/types/common";
import { twMerge } from "tailwind-merge";

export type AutocompleteOption = { label: any; value: any; id: number };

type Props = {
  disabled?: boolean;
  value?: Nullable<string>;
  options?: AutocompleteOption[];
  onSelect?: (_option: AutocompleteOption) => void;
  onChangeValue?: (text: string) => void;
  wrapperClassName?: string;
  className?: string;
  isFetchingData?: boolean;
  fetchData?: (query?: string) => void;
  placeholder?: string;
  itemClassName?: string;
  dropdownClassName?: string;
  onFocussed?: (bool: boolean) => void;
  name?: string;
};

const Autocomplete = ({
  disabled,
  options = [],
  onSelect,
  value,
  onChangeValue,
  className = "",
  isFetchingData,
  fetchData,
  placeholder = "",
  itemClassName,
  dropdownClassName,
  onFocussed,
  wrapperClassName,
  name,
}: Props) => {
  const [showPopup, setShowPopup] = useState<boolean>(false);
  const [activeIndex, setActiveIndex] = useState(0);
  const [query, setQuery] = useState("");

  const fetchDataDebounced = useMemo(
    () =>
      debounce((val) => {
        fetchData?.(val);
      }, 500),
    [fetchData]
  );

  const onChangeQuery = useCallback(
    (event: any) => {
      const { name, value } = event.target;
      setQuery(value);
      onChangeValue?.(value);
      fetchDataDebounced(value);
      if (!showPopup) {
        setShowPopup(true);
      }
    },
    [onChangeValue, fetchDataDebounced, showPopup]
  );

  const onClickOutside = useCallback(() => {
    if (query !== value) {
      if (query.length === 0) {
        onChangeValue?.("");
      } else if (value && value.length > 0 && options.length > 0) {
        setQuery(value);
      } else if (query.length > 0) {
        onChangeValue?.(query);
      }
    }
    setShowPopup(false);
    onFocussed?.(false);
  }, [query, value, onFocussed, options.length, onChangeValue]);

  useEffect(() => {
    fetchData?.();
  }, [fetchData]);

  useEffect(() => {
    if (value) {
      setQuery(value);
    }
  }, [value]);

  useEffect(() => {
    setActiveIndex(0);
  }, [options]);

  return (
    <>
      <div className={twMerge("relative w-full", wrapperClassName)}>
        <Input
          name={name}
          className={twMerge(`px-2.5 py-[14px] w-full text-sm`, className)}
          value={query}
          onChange={onChangeQuery}
          placeholder={placeholder}
          onFocus={() => {
            if (query.length === 0) {
              setShowPopup(true);
              setActiveIndex(0);
              onFocussed?.(true);
            }
          }}
          disabled={disabled}
        />
        {showPopup && (
          <AutocompleteItems
            options={options}
            activeIndex={activeIndex}
            setActiveIndex={setActiveIndex}
            onSelect={onSelect}
            onClosePopup={() => setShowPopup(false)}
            isFetchingData={isFetchingData}
            itemClassName={itemClassName}
            dropdownClassName={dropdownClassName}
          />
        )}
      </div>
      {showPopup && (
        <div
          className="drawer-overlay absolute inset-0"
          onClick={onClickOutside}
        />
      )}
    </>
  );
};

export default Autocomplete;
