'use client';

import { memo } from 'react';

import FormHelperText from '@/app/ui/form-helper-text';

import classNames from 'classnames';
import { twMerge } from 'tailwind-merge';

type Props = {
  wrapperclassName?: string;
  className?: string;
  label?: string;
  helperText?: string;
  required?: boolean;
  error?: boolean;
  isStringValue?: boolean;
} & React.HTMLProps<HTMLInputElement>;

const CurrencyInput = ({
  label = '',
  required,
  error,
  helperText,
  className,
  wrapperclassName = '',
  placeholder,
  value,
  isStringValue,
  ...props
}: Props) => {
  return (
    <div className={twMerge('relative', wrapperclassName)}>
      {!isStringValue && (
        <span
          className={classNames('text-sm font-bold', {
            'input-disabled': props?.disabled,
          })}
        >
          $
        </span>
      )}

      <input
        {...props}
        value={!!value ? (isStringValue ? value : Number(value).toLocaleString('en-US')) : ''}
        placeholder={placeholder ?? label}
        className={twMerge(
          classNames(
            'input input-bordered h-min min-h-min rounded focus:outline-none p-4',
            error && 'border-error',
          ),
          className,
        )}
        pattern='\d*'
        inputMode='numeric'
        autoComplete='off'
      />
      {helperText && (
        <div className='ml-2 absolute'>
          <FormHelperText error={error}>{helperText}</FormHelperText>
        </div>
      )}
    </div>
  );
};

export default memo(CurrencyInput);
