import FormHelperText from '@/app/ui/form-helper-text';

import classNames from 'classnames';
import { twMerge } from 'tailwind-merge';

type Props = {
  className?: string;
  label?: string;
  helperText?: string;
  required?: boolean;
  error?: boolean;
} & React.HTMLProps<HTMLTextAreaElement>;

const Textarea = ({
  label = '',
  required,
  error,
  helperText,
  className,
  placeholder,
  ...props
}: Props) => {
  return (
    <div className='relative'>
      <textarea
        {...props}
        placeholder={placeholder ?? label}
        className={twMerge(
          classNames(
            'input input-bordered h-min min-h-min rounded focus:outline-none p-4',
            error && 'border-error',
          ),
          className,
        )}
      />
      {helperText && (
        <div className='ml-2 absolute'>
          <FormHelperText error={error}>{helperText}</FormHelperText>
        </div>
      )}
    </div>
  );
};

export default Textarea;
