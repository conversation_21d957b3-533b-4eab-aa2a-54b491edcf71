"use client";

import { ReactNode, useCallback, useState } from "react";

import { Nullable, ProfilePartner } from "@/types/common";

import Image from "next/image";
import useSWR from "swr";

import ManageCalendarHeader from "./ManageCalendarHeader";
import Checkbox from "@/clients/ui/checkbox";
import Button from "@/clients/ui/button";
import { getBrokerages } from "@/app/actions/calendar";

type Props = {
  onBack: () => void;
  onClose: () => void;
  selectedDatesNode: ReactNode;
  source: Nullable<ProfilePartner>;
  setSource: (id: Nullable<ProfilePartner>) => void;
};

const BookingSource = ({ onClose, onBack, setSource, source }: Props) => {
  const [selected, setSelected] = useState<Nullable<ProfilePartner>>(source);
  const [openOtherBrokerages, setOpenOtherBrokerages] =
    useState<boolean>(false);
  const { data, isLoading } = useSWR("brokerages", () =>
    getBrokerages<{ count: number; next: string; results: ProfilePartner[] }>()
  );

  const onChangeCheckbox = useCallback((partner: ProfilePartner) => {
    setSelected(partner);
  }, []);

  const onSave = useCallback(() => {
    if (selected) {
      setSource(selected);
    }
    onBack();
  }, [selected, setSource, onBack]);

  return (
    <>
      <div className="bg-white w-full h-full flex flex-col gap-2 md:gap-4 p-4 md:p-0">
        <ManageCalendarHeader
          title="Source of Booking"
          onBack={onBack}
          onClose={onClose}
        />

        <div className="overflow-scroll md:pb-[90px] grid grid-cols-1 gap-2 my-2">
          {isLoading && (
            <div className="w-full h-[150px] flex-center-center">
              <div className="loading loading-spinner loading-md" />
            </div>
          )}
          {
            <>
              {(data?.results ?? [])?.map((_source, index) => (
                <div
                  key={index}
                  className="p-4 border flex justify-between rounded-lg"
                >
                  <div className="flex-center md:items-start md:flex-col gap-4 md:gap-2">
                    <Image
                      alt="Cnc logo"
                      src={_source.logo_desktop ?? "images/cnc-logo2.png"}
                      width="0"
                      height="0"
                      sizes="100vw"
                      className="w-auto h-[26px]"
                    />
                    <span className="text-sm md:text-xs">{_source.name}</span>
                  </div>
                  <Checkbox
                    checked={selected?.id === _source.id}
                    onChange={() => onChangeCheckbox(_source)}
                  />
                </div>
              ))}
              <Button
                className="rounded-lg text-sm font-normal"
                onClick={onSave}
              >
                Save
              </Button>
              <Button
                intent="secondary"
                className="rounded-lg text-sm font-normal"
                onClick={onBack}
              >
                Cancel
              </Button>
            </>
          }
        </div>
      </div>
    </>
  );
};

export default BookingSource;
