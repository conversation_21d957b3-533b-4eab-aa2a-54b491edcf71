"use client";

import { useCallback, useState } from "react";

import { Nullable, ProgressStatus } from "@/types/common";

import Autocomplete, { AutocompleteOption } from "@/clients/ui/autocomplete";
import But<PERSON> from "@/clients/ui/button";
import { getListingAddresses } from "@/app/actions/property";
import { ListingAddressPayload } from "@/types/property";
import { usePathname, useRouter, useSearchParams } from "next/navigation";

const AddressForm = () => {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [address, setAdress] = useState<string>("");
  const [selectedpropertyId, setSelectedPropertyId] =
    useState<Nullable<number>>(null);
  const [isFetching, setIsFetching] = useState<boolean>(false);
  const [progressStatus, setProgressStatus] = useState<ProgressStatus | null>(
    null
  );
  const [options, setOptions] = useState<AutocompleteOption[]>([]);

  const onSelectAddress = useCallback((option: AutocompleteOption) => {
    setAdress(option.value);
    setSelectedPropertyId(option.id);
  }, []);

  const fetchAddresses = useCallback((query = "") => {
    setIsFetching(true);
    getListingAddresses<{ results: ListingAddressPayload[] }>(query)
      .then(({ results }) => {
        setOptions(
          results.map((_address, index) => ({
            id: _address.listing_id,
            label: _address.address,
            value: _address.address,
          }))
        );
        setIsFetching(false);
      })
      .catch((err) => {
        console.error(err);
        setIsFetching(false);
      });
  }, []);

  const onContinue = useCallback(() => {
    if (address && selectedpropertyId) {
      const pathnameSplit = pathname.split("/");
      pathnameSplit[1] = selectedpropertyId.toString();
      router.push(pathnameSplit.join("/"));
    }
  }, [address, pathname, router, selectedpropertyId]);

  return (
    <>
      <div className="rounded-lg border border-[rgba(0,0,0,0.20)] p-2 md:p-4 my-4">
        <p className="text-xs md:text-sm">
          Enter your vacation rental listing address
        </p>
        <hr className="my-2" />
        <Autocomplete
          value={address}
          className="text-xs md:text-sm border-0 px-0 py-0 md:py-2 w-full"
          placeholder="Listing Address..."
          options={options}
          onChangeValue={(text: string) => setAdress(text)}
          isFetchingData={isFetching}
          fetchData={fetchAddresses}
          onSelect={onSelectAddress}
        />
      </div>
      <Button
        className="text-sm py-2 px-4 font-normal bg-carolina-blue text-white rounded-lg flex m-auto"
        disabled={progressStatus === ProgressStatus.LOADING}
        onClick={onContinue}
      >
        Continue
      </Button>
    </>
  );
};

export default AddressForm;
