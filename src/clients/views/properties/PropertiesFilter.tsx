"use client";

import <PERSON>ton from "@/clients/ui/button";
import { useCallback, useState } from "react";
import RentFilter from "./Filters/RentFilter";
import { Nullable } from "@/types/common";
import AddressAutocomplete from "../common/AddressAutocomplete";
import Image from "next/image";
import { ListingAddressPayload } from "@/types/property";
import { useRouter } from "next/navigation";

type FormState = {
  min_price: Nullable<number>;
  max_price: Nullable<number>;
  address: string;
};

const PropertiesFilter = () => {
  const router = useRouter();

  // Form state
  const [formData, setFormData] = useState<FormState>({
    // dates: [],
    // last_update: "",
    // area: [],
    // key_number: "",
    // owner_last_name: "",
    // nr_listing_id: "",
    // nights: "",
    // capacity_gte: "",
    min_price: null,
    max_price: null,
    address: "",
    // show_inactive: false,
    // show_without_price: false,
    // bedroom_num_gte: null,
    // air_conditioning_central: false,
    // air_conditioning_minisplit: false,
    // air_conditioning_windowunits: false,
    // pets_askowner: false,
    // pets_yes: false,
    // pets_no: false,
    // pool_private: false,
    // pool_community: false,
    // walk_to_beach: false,
    // waterfront: false,
    // water_views: false,
  });

  const onChangeRent = useCallback(
    (min: Nullable<number>, max: Nullable<number>) => {
      console.log({ min, max });
      setFormData((_f) => ({
        ..._f,
        min_price: min,
        max_price: max,
      }));
      router.push(`/properties?${(min = min)}`);
    },
    [router]
  );

  const onSelectAddress = useCallback((_a: ListingAddressPayload) => {
    setFormData((_f) => ({
      ..._f,
      address: _a.address,
    }));
  }, []);

  // UI state

  const [showAmenities, setShowAmenities] = useState(false);
  const [showMobileFilters, setShowMobileFilters] = useState(false);

  return (
    <div className="flex w-full gap-x-4">
      <div className="w-[80%] overflow-x-scroll">
        <div className="w-[220px]">
          <RentFilter
            minPrice={formData.min_price}
            maxPrice={formData.max_price}
            onChange={onChangeRent}
            max={30000}
          />
          <div className="flex items-center gap-x-2 border rounded  p-2 my-2">
            <Image
              alt="rent filter icon"
              src="/images/icons/street.svg"
              width={20}
              height={20}
            />
            <AddressAutocomplete
              placeholder="Street Address"
              className="!p-0 !text-sm"
              onSelectAddress={onSelectAddress}
            />
          </div>
          <div className="w-[220px]"></div>
        </div>
      </div>
      <div className="w-[20%] flex items-center flex-col gap-y-2">
        <Button className="w-full" intent="outline">
          Clear Filter
        </Button>
        <Button className="w-full">Add A Listing</Button>
      </div>
    </div>
  );
};

export default PropertiesFilter;
