'use client';

import Button from '@/clients/ui/button';
import { useCallback, useState } from 'react';
import RentFilter from './Filters/RentFilter';
import { Nullable } from '@/types/common';
import AddressAutocomplete from '../common/AddressAutocomplete';
import Image from 'next/image';
import { ListingAddressPayload } from '@/types/property';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import DateRangePicker from '@/clients/components/common/DateRangePicker';
import { parseDateString } from '@/utils/common';
import { format, isValid } from 'date-fns';
import { CalendarDaysIcon } from '@heroicons/react/24/outline';
import { DateRange } from 'react-day-picker';

type FormState = {
  min_price: Nullable<number>;
  max_price: Nullable<number>;
  address: string;
  dates: [Nullable<string>, Nullable<string>];
};

const PropertiesFilter = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  console.log({ address: searchParams.get('address') });
  const pathname = usePathname();

  // Form state
  const [formData, setFormData] = useState<FormState>({
    dates: [
      searchParams.get('start_date') ?? null,
      searchParams.get('end_date') ?? null,
    ],
    // last_update: "",
    // area: [],
    // key_number: "",
    // owner_last_name: "",
    // nr_listing_id: "",
    // nights: "",
    // capacity_gte: "",
    min_price: searchParams.get('min_price')
      ? parseInt(searchParams.get('min_price') ?? '0')
      : null,
    max_price: searchParams.get('max_price')
      ? parseInt(searchParams.get('max_price') ?? '0')
      : null,
    address: searchParams.get('address') ?? '',
    // show_inactive: false,
    // show_without_price: false,
    // bedroom_num_gte: null,
    // air_conditioning_central: false,
    // air_conditioning_minisplit: false,
    // air_conditioning_windowunits: false,
    // pets_askowner: false,
    // pets_yes: false,
    // pets_no: false,
    // pool_private: false,
    // pool_community: false,
    // walk_to_beach: false,
    // waterfront: false,
    // water_views: false,
  });

  const onChangeRent = useCallback(
    (min: Nullable<number>, max: Nullable<number>) => {
      console.log({ min, max });
      setFormData((_f) => ({
        ..._f,
        min_price: min,
        max_price: max,
      }));

      // Create new URLSearchParams from current search params
      const params = new URLSearchParams(searchParams.toString());

      // Update or add the min_price parameter
      if (min !== null) {
        params.set('min_price', min.toString());
      } else {
        params.delete('min_price');
      }

      // Update or add the max_price parameter
      if (max !== null) {
        params.set('max_price', max.toString());
      } else {
        params.delete('max_price');
      }

      // Navigate to the updated URL
      router.push(`${pathname}?${params.toString()}`);
    },
    [router, searchParams, pathname]
  );

  const onSelectAddress = useCallback(
    (_a: ListingAddressPayload) => {
      setFormData((_f) => ({
        ..._f,
        address: _a.address,
      }));
      // Create new URLSearchParams from current search params
      const params = new URLSearchParams(searchParams.toString());

      // Update or add the min_price parameter
      if (_a.address !== '') {
        params.set('address', _a.address);
      } else {
        params.delete('address');
      }

      // Navigate to the updated URL
      router.push(`${pathname}?${params.toString()}`);
    },
    [pathname, router, searchParams]
  );

  const onChangeDateRange = useCallback(
    (_d?: DateRange) => {
      setFormData((_f) => ({
        ..._f,
        dates: [
          _d?.from ? format(_d?.from, 'yyyy-MM-dd') : null,
          _d?.to ? format(_d?.to, 'yyyy-MM-dd') : null,
        ],
      }));
      const params = new URLSearchParams(searchParams.toString());

      if (_d?.from && _d.to) {
        console.log('_d', _d, format(_d?.from, 'yyyy-MM-dd'));
        params.set('start_date', format(_d?.from, 'yyyy-MM-dd'));
        params.set('end_date', format(_d?.to, 'yyyy-MM-dd'));
      } else {
        params.delete('start_date');
        params.delete('end_date');
      }

      // Navigate to the updated URL
      router.push(`${pathname}?${params.toString()}`);
    },
    [pathname, router, searchParams]
  );

  // UI state

  const [showAmenities, setShowAmenities] = useState(false);
  const [showMobileFilters, setShowMobileFilters] = useState(false);

  console.log({
    ...formData,
  });

  return (
    <>
      <div className="w-full flex gap-x-2">
        <div className="w-[240px]">
          <RentFilter
            minPrice={formData.min_price}
            maxPrice={formData.max_price}
            onChange={onChangeRent}
            max={30000}
          />
          <div className="flex items-center gap-x-2 border rounded my-2 relative w-full">
            <Image
              alt="rent filter icon"
              src="/images/icons/street.svg"
              width={20}
              height={20}
              className="absolute left-2 z-10"
            />
            <AddressAutocomplete
              placeholder="Street Address"
              className="!text-sm !p-0 !pl-6"
              wrapperClassName="w-full p-2"
              value={formData.address}
              onSelectAddress={onSelectAddress}
            />
          </div>
        </div>
        <div className="w-[240px]">
          <DateRangePicker
            date={{
              from: formData.dates[0]
                ? parseDateString(formData.dates[0])
                : undefined,
              to: formData.dates[1]
                ? parseDateString(formData.dates[1])
                : undefined,
            }}
            setDate={onChangeDateRange}
            className="border rounded"
            enableAllDates
            title={
              <div className="text-sm text-roman-silver flex items-center gap-x-2">
                <CalendarDaysIcon className="w-5 h-5" />
                <span className="truncate">
                  {formData.dates.every((_d) =>
                    isValid(parseDateString(_d ?? ''))
                  )
                    ? `${format(
                        formData.dates[0] ?? '',
                        'dd/MM/yyyy'
                      )} - ${format(formData.dates[1] ?? '', 'dd/MM/yyyy')}`
                    : `Date Range`}
                </span>
              </div>
            }
          />
        </div>
      </div>
      <div className="flex items-center gap-x-2 my-2">
        <Button className="w-[240px]" intent="outline">
          Clear Filter
        </Button>
        <Button className="w-[240px]">Add A Listing</Button>
      </div>
    </>
  );
};

export default PropertiesFilter;
