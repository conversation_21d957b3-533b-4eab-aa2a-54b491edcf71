import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import { formatPrice, truncate } from '../../lib/utils';
import { usePropertiesStore } from '../../store/propertiesStore';
import LoadingSpinner from '../common/LoadingSpinner';

const PropertiesTable = ({
  data = [],
  total = 0,
  flag = false,
  offset = 0,
  currentPage = 1,
  searchInfor = {},
  limit = 25,
  onPagesChanged,
  onSort,
  onSubset,
  onShowAllPage,
  onLoad,
}) => {
  const router = useRouter();
  const { setPropertiesChecked } = usePropertiesStore();

  // State
  const [selectedItems, setSelectedItems] = useState([]);
  const [sortConfig, setSortConfig] = useState({ key: null, direction: 'asc' });
  const [showActions, setShowActions] = useState(false);
  const [loading, setLoading] = useState(false);

  // Handle item selection
  const handleSelectItem = (item) => {
    setSelectedItems((prev) => {
      const isSelected = prev.find(
        (selected) => selected.listing_id === item.listing_id
      );
      let newSelection;

      if (isSelected) {
        newSelection = prev.filter(
          (selected) => selected.listing_id !== item.listing_id
        );
      } else {
        newSelection = [...prev, item];
      }

      setPropertiesChecked(newSelection);
      setShowActions(newSelection.length > 0);
      return newSelection;
    });
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectedItems.length === data.length) {
      setSelectedItems([]);
      setPropertiesChecked([]);
      setShowActions(false);
    } else {
      setSelectedItems(data);
      setPropertiesChecked(data);
      setShowActions(true);
    }
  };

  // Handle sorting
  const handleSort = (key) => {
    let direction = 'asc';
    if (sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
    onSort({
      prop: key,
      order: direction === 'asc' ? 'ascending' : 'descending',
    });
  };

  // Handle pagination
  const handlePageChange = (page) => {
    onPagesChanged(page);
  };

  // Handle subset (show only selected)
  const handleSubset = () => {
    onSubset(selectedItems);
    setShowActions(false);
  };

  // Handle property actions
  const handleViewProperty = (property) => {
    router.push(`/properties/${property.listing_id}`);
  };

  const handleEditProperty = (property) => {
    router.push(`/properties/${property.listing_id}/edit`);
  };

  const handleCreateLease = (property) => {
    router.push(`/leases/create?listing=${property.listing_id}`);
  };

  // Generate pagination
  const generatePagination = () => {
    const totalPages = Math.ceil(total / limit);
    const pages = [];
    const maxVisiblePages = 5;

    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage < maxVisiblePages - 1) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }

    return { pages, totalPages, startPage, endPage };
  };

  const { pages, totalPages } = generatePagination();

  // Table columns configuration
  const columns = [
    { key: 'checkbox', label: '', sortable: false, width: 'w-12' },
    { key: 'listing_id', label: 'ID', sortable: true, width: 'w-20' },
    { key: 'address', label: 'Address', sortable: true, width: 'w-64' },
    { key: 'owner_last_name', label: 'Owner', sortable: true, width: 'w-32' },
    {
      key: 'nr_listing_id',
      label: 'NR Listing ID',
      sortable: true,
      width: 'w-32',
    },
    { key: 'key_number', label: 'Key #', sortable: true, width: 'w-24' },
    { key: 'bedroom_num', label: 'Beds', sortable: true, width: 'w-20' },
    { key: 'capacity', label: 'Capacity', sortable: true, width: 'w-24' },
    { key: 'price', label: 'Price', sortable: true, width: 'w-24' },
    { key: 'status', label: 'Status', sortable: true, width: 'w-24' },
    { key: 'actions', label: 'Actions', sortable: false, width: 'w-32' },
  ];

  return (
    <div className="bg-white shadow-sm border border-gray-200 rounded-lg overflow-hidden">
      {/* Actions Bar */}
      {showActions && (
        <div className="bg-blue-50 border-b border-blue-200 px-6 py-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-blue-700">
              {selectedItems.length} item{selectedItems.length !== 1 ? 's' : ''}{' '}
              selected
            </span>
            <div className="flex items-center space-x-3">
              <button
                onClick={handleSubset}
                className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
              >
                Subset
              </button>
              <button
                onClick={() => {
                  /* Handle share listings */
                }}
                className="bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
              >
                Share Listings
              </button>
              <button
                onClick={() => {
                  /* Handle email owners */
                }}
                className="bg-purple-600 hover:bg-purple-700 text-white px-3 py-1 rounded text-sm font-medium transition-colors"
              >
                Email Owners
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Table */}
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              {columns.map((column) => (
                <th
                  key={column.key}
                  className={`px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider ${column.width}`}
                >
                  {column.key === 'checkbox' ? (
                    <input
                      type="checkbox"
                      checked={
                        selectedItems.length === data.length && data.length > 0
                      }
                      onChange={handleSelectAll}
                      className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                    />
                  ) : (
                    <div className="flex items-center space-x-1">
                      <span>{column.label}</span>
                      {column.sortable && (
                        <button
                          onClick={() => handleSort(column.key)}
                          className="text-gray-400 hover:text-gray-600"
                        >
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M7 16V4m0 0L3 8m4-4l4 4m6 0v12m0 0l4-4m-4 4l-4-4"
                            />
                          </svg>
                        </button>
                      )}
                    </div>
                  )}
                </th>
              ))}
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {data.map((property, index) => (
              <tr
                key={property.listing_id}
                className={`hover:bg-gray-50 ${
                  selectedItems.find(
                    (item) => item.listing_id === property.listing_id
                  )
                    ? 'bg-blue-50'
                    : ''
                }`}
              >
                <td className="px-6 py-4 whitespace-nowrap">
                  <input
                    type="checkbox"
                    checked={
                      !!selectedItems.find(
                        (item) => item.listing_id === property.listing_id
                      )
                    }
                    onChange={() => handleSelectItem(property)}
                    className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                  />
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {property.listing_id}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <div className="text-sm text-gray-900">
                    {truncate(property.address || 'N/A', 40)}
                  </div>
                  {property.area && (
                    <div className="text-xs text-gray-500">{property.area}</div>
                  )}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {property.owner_last_name || 'N/A'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {property.nr_listing_id || 'N/A'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {property.key_number || 'N/A'}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {property.bedroom_num || 0}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {property.capacity || 0}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                  {formatPrice(property.price)}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${
                      property.active
                        ? 'bg-green-100 text-green-800'
                        : 'bg-red-100 text-red-800'
                    }`}
                  >
                    {property.active ? 'Active' : 'Inactive'}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                  <div className="flex items-center space-x-2">
                    <button
                      onClick={() => handleViewProperty(property)}
                      className="text-blue-600 hover:text-blue-900 transition-colors"
                      title="View"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        />
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                        />
                      </svg>
                    </button>
                    <button
                      onClick={() => handleEditProperty(property)}
                      className="text-gray-600 hover:text-gray-900 transition-colors"
                      title="Edit"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                        />
                      </svg>
                    </button>
                    <button
                      onClick={() => handleCreateLease(property)}
                      className="text-green-600 hover:text-green-900 transition-colors"
                      title="Create Lease"
                    >
                      <svg
                        className="w-4 h-4"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                        />
                      </svg>
                    </button>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Empty State */}
      {data.length === 0 && !loading && (
        <div className="text-center py-12">
          <svg
            className="mx-auto h-12 w-12 text-gray-400"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-4m-5 0H3m2 0h3M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
            />
          </svg>
          <h3 className="mt-2 text-sm font-medium text-gray-900">
            No properties found
          </h3>
          <p className="mt-1 text-sm text-gray-500">
            Try adjusting your search criteria or add a new property.
          </p>
        </div>
      )}

      {/* Loading State */}
      {loading && (
        <div className="text-center py-12">
          <LoadingSpinner size="large" />
        </div>
      )}

      {/* Pagination */}
      {data.length > 0 && (
        <div className="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center">
              <p className="text-sm text-gray-700">
                Showing <span className="font-medium">{offset + 1}</span> to{' '}
                <span className="font-medium">
                  {Math.min(offset + limit, total)}
                </span>{' '}
                of <span className="font-medium">{total}</span> results
              </p>
              <button
                onClick={onShowAllPage}
                className="ml-4 text-sm text-blue-600 hover:text-blue-800 transition-colors"
              >
                Show all on one page
              </button>
            </div>
            <div className="flex items-center space-x-2">
              <button
                onClick={() => handlePageChange(currentPage - 1)}
                disabled={currentPage === 1}
                className="relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg
                  className="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M15 19l-7-7 7-7"
                  />
                </svg>
              </button>

              {pages.map((page) => (
                <button
                  key={page}
                  onClick={() => handlePageChange(page)}
                  className={`relative inline-flex items-center px-4 py-2 border text-sm font-medium ${
                    page === currentPage
                      ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                      : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                  }`}
                >
                  {page}
                </button>
              ))}

              <button
                onClick={() => handlePageChange(currentPage + 1)}
                disabled={currentPage === totalPages}
                className="relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <svg
                  className="h-5 w-5"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M9 5l7 7-7 7"
                  />
                </svg>
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Load More Button for Infinite Scroll */}
      {data.length > 0 && currentPage * limit < total && (
        <div className="text-center py-4 border-t border-gray-200">
          <button
            onClick={onLoad}
            className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-md text-sm font-medium transition-colors"
          >
            Load More
          </button>
        </div>
      )}
    </div>
  );
};

export default PropertiesTable;
