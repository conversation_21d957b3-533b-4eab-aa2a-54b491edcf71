import { Nullable } from "./common";

export type UserProfile = {
  user_id: number;
  has_usable_password: boolean;
  is_superuser: boolean;
  username: string;
  login_email: string;
  is_admin: boolean;
  is_staff: boolean;
  is_active: boolean;
  first_name: string;
  last_name: string;
  avatar: Nullable<string>;
  email: string;
  email_verified: false;
  phone: Nullable<string>;
};

export type Contact = {
  contact_id: number;
  created_at: string;
  first_name: string;
  last_name: string;
  email1: string;
  cell_phone: Nullable<string>;
  street1: Nullable<string>;
  street2: Nullable<string>;
  city: Nullable<string>;
  state: Nullable<string>;
  country: Nullable<string>;
  types: string[];
  owner_id: number;
  owner_first_name: string;
  owner_last_name: string;
  leases: {
    lease_id: number;
    rental_address: string;
  }[];
  last_contact_date: Nullable<string>;
};
