import {
  BlockedType,
  PropertyRentalRates,
  UpdateLogType,
} from "@/types/calendar";

import dayjs, { Dayjs } from "dayjs";
import { currencyFormatter, isJsonString } from "./common";
import { PropertyAvailability } from "@/types/calendar";
import { Brokerage } from "@/types/property";
import { Nullable } from "@/types/common";

export type MonthList = {
  month: number;
  year: number;
}[];

export const getMonthYearsList = (year?: number): MonthList => {
  const currentMonth = dayjs(year && `${year}`, `YYYY`).startOf("year");

  return getMonthsBetweenDates(
    currentMonth.format("YYYY-MM-DD"),
    currentMonth.add(11, "months").format("YYYY-MM-DD")
  );
};

export const getMonthsBetweenDates = (startDate?: string, endDate?: string) => {
  const months: MonthList = [];
  if (!startDate || !endDate) {
    return months;
  }
  const start = dayjs(startDate, "YYYY-MM-DD").startOf("month");
  const end = dayjs(endDate, "YYYY-MM-DD").endOf("month");
  const numberOfMonths = end.diff(start, "month");

  const current = start;
  for (let i = 0; i <= numberOfMonths; i++) {
    months.push({
      year: current.add(i, "month").year(),
      month: current.add(i, "month").month(),
    });
  }

  return months;
};

export const getNumberOfDaysInMonth = (year: number, month: number): number => {
  return dayjs(`${year}-${month + 1}`, "YYYY-M").daysInMonth();
};

export const getFirstDayOfMonth = (year: number, month: number) => {
  return dayjs(`${year}-${month + 1}`, "YYYY-M")
    .startOf("month")
    .day();
};

export const isBetweenTwoDates = (
  startDate: Dayjs | string | null,
  endDate: Dayjs | string | null,
  date: Dayjs,
  excludeStartAndEnd?: boolean
): boolean => {
  const start = dayjs(startDate, "YYYY-MM-DD").startOf("day");
  const end = dayjs(endDate, "YYYY-MM-DD").endOf("day");
  if (excludeStartAndEnd) {
    return date.isBetween(start, end);
  }
  return date.isBetween(start, end) || date.isSame(start) || date.isSame(end);
};

export const formatAvailabilityDateString = (date?: string): Dayjs =>
  dayjs(date, "YYYY-MM-DD");

export const checkIfStringIsValidDate = (
  dateString: string,
  format = "YYYY-MM-DD"
): boolean => {
  return dayjs(dateString, format).isValid();
};

export const getAvailabilityDataForCurrentMonth = (
  availabilityData: PropertyAvailability[],
  currentMonth: number,
  currentYear: number
) => {
  const dateObject = dayjs(`${currentYear}/${currentMonth}/1`, "YYYY/M/D");
  const minDate = dateObject.subtract(1, "day");
  const maxDate = dayjs(dateObject)
    .add(1, "month")
    .startOf("month")
    .add(7, "days");
  return availabilityData.filter(
    (_a) =>
      dayjs(_a.to_date).isBetween(minDate, maxDate) ||
      isRangeWithin(
        minDate.format("YYYY-MM-DD"),
        maxDate.format("YYYY-MM-DD"),
        _a.from_date,
        _a.to_date
      ) ||
      dayjs(_a.from_date).isBetween(minDate, maxDate)
  );
};

export const getRentalRatesForCurrentMonth = (
  rentalRates: PropertyRentalRates[],
  currentMonth: number,
  currentYear: number
) => {
  const dateObject = dayjs(`${currentYear}/${currentMonth}/1`, "YYYY/M/D");
  const minDate = dateObject.subtract(1, "day");
  const maxDate = dayjs(dateObject).add(1, "month").startOf("month");
  return rentalRates.filter((_a) =>
    dayjs(_a.from_date).isBetween(minDate, maxDate)
  );
};

export const getDateRangeStartAndEnd = (
  range: PropertyAvailability[],
  dateObject: Dayjs,
  index: number = 0
) => {
  const { from_date, to_date } = range[index] || {};
  const start = dateObject.isSame(formatAvailabilityDateString(from_date));
  const end = dateObject.isSame(formatAvailabilityDateString(to_date));
  return [range.length > 0, start, end];
};

export const getAvailabilityTitle = (blockType: BlockedType) => {
  switch (blockType) {
    case BlockedType.LEASED:
      return "Leased";
    case BlockedType.PENDING:
      return "Pending";
    default:
      return "Blocked";
  }
};

export const getAvailabilityDataForRange = (
  availabilityData: PropertyAvailability[],
  startDate: Dayjs | string | null,
  endDate: Dayjs | string | null
) => {
  return availabilityData.filter(({ from_date, to_date }) => {
    if (startDate && !endDate) {
      return (
        dayjs(from_date).isSame(startDate) ||
        dayjs(to_date).isSame(startDate) ||
        isBetweenTwoDates(from_date, to_date, dayjs(startDate))
      );
    }

    if (startDate && endDate) {
      return (
        dayjs(from_date).isSame(startDate) ||
        dayjs(to_date).isSame(startDate) ||
        isBetweenTwoDates(startDate, endDate, dayjs(from_date)) ||
        isBetweenTwoDates(startDate, endDate, dayjs(to_date))
      );
    }
  });
};

export const checkIfTwoRangesOverlaps = (
  range1: [string, string],
  range2: [string, string]
): boolean => {
  const start = dayjs(range1[0], "YYYY-MM-DD").startOf("day");
  const end = dayjs(range1[1], "YYYY-MM-DD").startOf("day");
  const start2 = dayjs(range2[0], "YYYY-MM-DD").startOf("day");
  const end2 = dayjs(range2[1], "YYYY-MM-DD").startOf("day");
  return (
    start.isBetween(start2, end2) ||
    end.isBetween(start2, end2) ||
    start2.isBetween(start, end) ||
    end2.isBetween(start, end)
  );
};

export const formatUpdateLogContentString = (
  value: string | boolean,
  type: UpdateLogType,
  contentType?: string
) => {
  if (type === UpdateLogType.VALIDATE) {
    return `Validated by homeowner via email link`;
  }
  if (type === UpdateLogType.AVAILABILITY) {
    return !!value
      ? `Available`
      : contentType === "owner"
      ? "Owner Time"
      : "Other Office";
  }

  if (type === UpdateLogType.RATES) {
    return currencyFormatter.format(Number(value));
  }
};

export const checkIfBrokerageIsCNCorNR = (brokerage: Nullable<Brokerage>) => {
  if (!brokerage) {
    return false;
  }
  if (
    new RegExp("Congdon and Coleman", "i").test(brokerage.name) ||
    new RegExp("Congdon & Coleman", "i").test(brokerage.name) ||
    new RegExp("Nantucket Rentals", "i").test(brokerage.name) ||
    brokerage.id === 1
  ) {
    return true;
  }
};

/**
 * checks if [start1, end1] is inside [start2, end2]
 * @param start1
 * @param end1
 * @param start2
 * @param end2
 * @returns
 */
export const isRangeWithin = (
  start1: string,
  end1: string,
  start2: string,
  end2: string
) => {
  return (
    dayjs(start1).isBetween(start2, end2, null, "[)") &&
    dayjs(end1).isBetween(start2, end2, null, "(]")
  );
};
